#!/usr/bin/env python3
"""
Script to translate English CSV content to Korean using GPT-4.1 model.
Outputs clean translations without prefixes in JSON format.
"""

import csv
import json
import openai
import time
from typing import List, Dict, Any
import os
import getpass


def setup_openai_client():
    """Set up OpenAI client with API key."""
    api_key = os.getenv("OPENAI_API_KEY")

    if not api_key:
        print("OpenAI API key not found in environment variables.")
        api_key = getpass.getpass("Please enter your OpenAI API key: ")

    return openai.OpenAI(api_key=api_key)


# Set up OpenAI client
client = None


def translate_text_to_korean(text: str, context: str = "") -> str:
    """
    Translate English text to Korean using GPT-4.1 model.

    Args:
        text: English text to translate
        context: Additional context for better translation

    Returns:
        Korean translation
    """
    system_prompt = """You are a professional translator specializing in English to Korean translation. 
    Translate the given English text to natural, fluent Korean. 
    - Maintain the original meaning and tone
    - Use appropriate Korean honorifics and formal language where suitable
    - For cultural references, use Korean equivalents when they exist
    - Return ONLY the Korean translation without any prefixes like "Here's the translation:" or explanatory text
    - Ensure the translation sounds natural to native Korean speakers"""

    user_prompt = f"Translate this English text to Korean: {text}"
    if context:
        user_prompt += f"\n\nContext: {context}"

    try:
        response = client.chat.completions.create(
            model="gpt-4-1106-preview",  # GPT-4.1 model
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            max_completion_tokens=1000,
            temperature=0.3,
        )

        return response.choices[0].message.content.strip()

    except Exception as e:
        print(f"Translation error: {e}")
        return text  # Return original text if translation fails


def translate_csv_row(row: Dict[str, str]) -> Dict[str, str]:
    """
    Translate a single CSV row to Korean.

    Args:
        row: Dictionary containing the CSV row data

    Returns:
        Dictionary with Korean translations
    """
    translated_row = {}

    # Translate question
    if "Question" in row and row["Question"]:
        translated_row["Question"] = translate_text_to_korean(
            row["Question"], "This is a multiple choice question about Korean culture"
        )
        time.sleep(0.5)  # Rate limiting

    # Translate options
    for i in range(1, 5):
        option_key = f"Option {i}"
        if option_key in row and row[option_key]:
            translated_row[option_key] = translate_text_to_korean(
                row[option_key], "This is a multiple choice answer option"
            )
            time.sleep(0.5)  # Rate limiting

    return translated_row


def main():
    """Main function to process the CSV file and create Korean translation."""

    global client

    # Initialize OpenAI client
    try:
        client = setup_openai_client()
        print("OpenAI client initialized successfully.")
    except Exception as e:
        print(f"Error setting up OpenAI client: {e}")
        return

    input_file = "english.csv"
    output_json = "korean_translation.json"

    if not os.path.exists(input_file):
        print(f"Error: {input_file} not found!")
        return

    translated_data = []

    print("Starting translation process...")

    try:
        with open(input_file, "r", encoding="utf-8") as file:
            csv_reader = csv.DictReader(file)

            for i, row in enumerate(csv_reader, 1):
                print(f"Translating row {i}...")

                translated_row = translate_csv_row(row)
                translated_data.append(
                    {"row_number": i, "original": row, "korean": translated_row}
                )

                # Save progress every 10 rows
                if i % 10 == 0:
                    with open(output_json, "w", encoding="utf-8") as outfile:
                        json.dump(
                            translated_data, outfile, ensure_ascii=False, indent=2
                        )
                    print(f"Progress saved: {i} rows completed")

    except Exception as e:
        print(f"Error processing file: {e}")
        return

    # Final save
    try:
        with open(output_json, "w", encoding="utf-8") as outfile:
            json.dump(translated_data, outfile, ensure_ascii=False, indent=2)

        print(f"\nTranslation completed!")
        print(f"Total rows translated: {len(translated_data)}")
        print(f"Output saved to: {output_json}")

        # Also create a simple Korean CSV file
        korean_csv = "korean.csv"
        with open(korean_csv, "w", encoding="utf-8", newline="") as csvfile:
            fieldnames = ["Question", "Option 1", "Option 2", "Option 3", "Option 4"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for item in translated_data:
                writer.writerow(item["korean"])

        print(f"Korean CSV also saved to: {korean_csv}")

    except Exception as e:
        print(f"Error saving output: {e}")


if __name__ == "__main__":
    main()
