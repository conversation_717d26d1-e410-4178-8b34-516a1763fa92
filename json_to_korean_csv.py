#!/usr/bin/env python3
"""
<PERSON>ript to convert Korean translation JSON to CSV format.
Extracts only the Korean translations and saves them as a clean CSV file.
"""

import json
import csv
import os
from typing import List, Dict, Any

def load_translation_json(json_file: str) -> List[Dict[str, Any]]:
    """
    Load the translation JSON file.
    
    Args:
        json_file: Path to the JSON file containing translations
    
    Returns:
        List of translation data
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as file:
            data = json.load(file)
        return data
    except FileNotFoundError:
        print(f"Error: {json_file} not found!")
        return []
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON file: {e}")
        return []
    except Exception as e:
        print(f"Error loading file: {e}")
        return []

def extract_korean_data(translation_data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
    """
    Extract only the Korean translations from the data.
    
    Args:
        translation_data: List of translation dictionaries
    
    Returns:
        List of Korean translation rows
    """
    korean_rows = []
    
    for item in translation_data:
        if 'korean' in item and item['korean']:
            korean_row = {}
            korean_data = item['korean']
            
            # Extract question and options
            if 'Question' in korean_data:
                korean_row['Question'] = korean_data['Question']
            
            # Extract all options
            for i in range(1, 5):
                option_key = f'Option {i}'
                if option_key in korean_data:
                    korean_row[option_key] = korean_data[option_key]
                else:
                    korean_row[option_key] = ''  # Empty if not present
            
            korean_rows.append(korean_row)
    
    return korean_rows

def save_korean_csv(korean_data: List[Dict[str, str]], output_file: str) -> bool:
    """
    Save Korean data to CSV file.
    
    Args:
        korean_data: List of Korean translation rows
        output_file: Output CSV file path
    
    Returns:
        True if successful, False otherwise
    """
    if not korean_data:
        print("No Korean data to save!")
        return False
    
    try:
        fieldnames = ['Question', 'Option 1', 'Option 2', 'Option 3', 'Option 4']
        
        with open(output_file, 'w', encoding='utf-8', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            # Write header
            writer.writeheader()
            
            # Write data rows
            for row in korean_data:
                writer.writerow(row)
        
        return True
    
    except Exception as e:
        print(f"Error saving CSV file: {e}")
        return False

def print_sample_data(korean_data: List[Dict[str, str]], num_samples: int = 3):
    """
    Print sample Korean data for verification.
    
    Args:
        korean_data: List of Korean translation rows
        num_samples: Number of samples to display
    """
    print(f"\nSample Korean translations (showing first {num_samples} rows):")
    print("-" * 80)
    
    for i, row in enumerate(korean_data[:num_samples], 1):
        print(f"\nRow {i}:")
        print(f"Question: {row.get('Question', 'N/A')}")
        for j in range(1, 5):
            option_key = f'Option {j}'
            print(f"{option_key}: {row.get(option_key, 'N/A')}")
        print("-" * 40)

def main():
    """Main function to convert JSON to Korean CSV."""
    
    input_json = 'korean_translation.json'
    output_csv = 'korean.csv'
    
    print("Converting Korean translation JSON to CSV...")
    
    # Load translation data
    print(f"Loading translation data from {input_json}...")
    translation_data = load_translation_json(input_json)
    
    if not translation_data:
        print("No translation data found. Exiting.")
        return
    
    print(f"Loaded {len(translation_data)} translation entries.")
    
    # Extract Korean data
    print("Extracting Korean translations...")
    korean_data = extract_korean_data(translation_data)
    
    if not korean_data:
        print("No Korean translation data found. Exiting.")
        return
    
    print(f"Extracted {len(korean_data)} Korean translation rows.")
    
    # Save to CSV
    print(f"Saving Korean translations to {output_csv}...")
    success = save_korean_csv(korean_data, output_csv)
    
    if success:
        print(f"✅ Successfully saved Korean translations to {output_csv}")
        print(f"Total rows: {len(korean_data)}")
        
        # Show sample data
        print_sample_data(korean_data)
        
        # File size info
        if os.path.exists(output_csv):
            file_size = os.path.getsize(output_csv)
            print(f"\nOutput file size: {file_size:,} bytes")
    else:
        print("❌ Failed to save Korean translations.")

if __name__ == "__main__":
    main()
